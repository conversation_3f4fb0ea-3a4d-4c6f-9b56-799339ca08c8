---
type: "always_apply"
---

# @xyflow/react开发规范 - Windsurf配置
project_context: |
  这是一个使用@xyflow/react库开发的节点编辑器项目
  
coding_standards:
  - 使用@xyflow/react最新版本(12.4+)
  - 所有自定义节点必须实现TypeScript类型定义
  - 性能敏感组件必须使用React.memo
  - 遵循函数式组件和Hook模式
  
specific_patterns:
  node_development: |
    自定义节点开发模式：
    1. 组件使用memo包装
    2. Handle组件指定position和id
    3. 数据传递通过data属性
    4. 样式使用CSS modules或styled-components
    
  state_management: |
    状态管理模式：
    1. 使用useNodesState/useEdgesState进行基础状态管理
    2. 复杂应用考虑zustand集成
    3. 事件处理函数使用useCallback包装
    4. 避免直接修改nodes/edges数组

performance_requirements:
  - 节点数量超过50个时必须实现memoization
  - 避免在组件渲染中进行昂贵计算
  - 使用useMemo缓存计算结果
  - 大型数据集使用虚拟化技术