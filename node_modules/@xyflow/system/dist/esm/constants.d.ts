import { CoordinateExtent, HandleType } from './types';
export declare const errorMessages: {
    error001: () => string;
    error002: () => string;
    error003: (nodeType: string) => string;
    error004: () => string;
    error005: () => string;
    error006: () => string;
    error007: (id: string) => string;
    error009: (type: string) => string;
    error008: (handleType: HandleType, { id, sourceHandle, targetHandle }: {
        id: string;
        sourceHandle: string | null;
        targetHandle: string | null;
    }) => string;
    error010: () => string;
    error011: (edgeType: string) => string;
    error012: (id: string) => string;
    error013: (lib?: string) => string;
    error014: () => string;
    error015: () => string;
};
export declare const infiniteExtent: CoordinateExtent;
export declare const elementSelectionKeys: string[];
export declare const defaultAriaLabelConfig: {
    'node.a11yDescription.default': string;
    'node.a11yDescription.keyboardDisabled': string;
    'node.a11yDescription.ariaLiveMessage': ({ direction, x, y }: {
        direction: string;
        x: number;
        y: number;
    }) => string;
    'edge.a11yDescription.default': string;
    'controls.ariaLabel': string;
    'controls.zoomIn.ariaLabel': string;
    'controls.zoomOut.ariaLabel': string;
    'controls.fitView.ariaLabel': string;
    'controls.interactive.ariaLabel': string;
    'minimap.ariaLabel': string;
    'handle.ariaLabel': string;
};
export type AriaLabelConfig = typeof defaultAriaLabelConfig;
//# sourceMappingURL=constants.d.ts.map