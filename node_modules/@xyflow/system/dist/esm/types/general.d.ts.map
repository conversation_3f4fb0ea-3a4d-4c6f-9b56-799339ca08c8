{"version": 3, "file": "general.d.ts", "sourceRoot": "", "sources": ["../../src/types/general.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,SAAS,IAAI,WAAW,EAAE,MAAM,cAAc,CAAC;AAC7D,OAAO,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AAC5D,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAI5C,OAAO,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AAC1D,OAAO,KAAK,EAAE,gBAAgB,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AACxE,OAAO,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AACpD,OAAO,EAAE,eAAe,EAAE,MAAM,WAAW,CAAC;AAC5C,OAAO,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;AAE9B,MAAM,MAAM,OAAO,GAAG,CAAC,QAAQ,EAAE,UAAU,KAAK,UAAU,CAAC;AAE3D;;GAEG;AACH,MAAM,MAAM,MAAM,GAAG,CAAC,KAAK,EAAE,UAAU,GAAG,UAAU,GAAG,IAAI,EAAE,QAAQ,EAAE,QAAQ,KAAK,IAAI,CAAC;AACzF,MAAM,MAAM,WAAW,GAAG,MAAM,CAAC;AACjC,MAAM,MAAM,SAAS,GAAG,MAAM,CAAC;AAE/B;;GAEG;AACH,MAAM,MAAM,SAAS,GAAG,CAAC,OAAO,CAAC,EAAE,6BAA6B,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;AACtF;;GAEG;AACH,MAAM,MAAM,MAAM,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,6BAA6B,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;AACtG;;GAEG;AACH,MAAM,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC;AACnC;;GAEG;AACH,MAAM,MAAM,WAAW,GAAG,MAAM,QAAQ,CAAC;AAEzC;;;;;;GAMG;AACH,MAAM,MAAM,WAAW,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,6BAA6B,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;AAE5G;;;;;;;GAOG;AACH,MAAM,MAAM,SAAS,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,gBAAgB,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;AAE/F;;;;;;GAMG;AACH,MAAM,MAAM,SAAS,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,EAAE,gBAAgB,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;AAEvF;;;;;;GAMG;AACH,MAAM,MAAM,UAAU,GAAG;IACvB,0DAA0D;IAC1D,MAAM,EAAE,MAAM,CAAC;IACf,wDAAwD;IACxD,MAAM,EAAE,MAAM,CAAC;IACf,qGAAqG;IACrG,YAAY,EAAE,MAAM,GAAG,IAAI,CAAC;IAC5B,mGAAmG;IACnG,YAAY,EAAE,MAAM,GAAG,IAAI,CAAC;CAC7B,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,gBAAgB,GAAG,UAAU,GAAG;IAC1C,MAAM,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,cAAc,GAAG,UAAU,GAAG;IACxC,MAAM,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF;;;;;;GAMG;AACH,oBAAY,cAAc;IACxB,MAAM,WAAW;IACjB,KAAK,UAAU;CAChB;AAED,MAAM,MAAM,oBAAoB,GAAG;IACjC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB,UAAU,EAAE,UAAU,GAAG,IAAI,CAAC;CAC/B,CAAC;AAEF,MAAM,MAAM,cAAc,GAAG,CAAC,KAAK,EAAE,UAAU,GAAG,UAAU,EAAE,MAAM,EAAE,oBAAoB,KAAK,IAAI,CAAC;AACpG,MAAM,MAAM,SAAS,GAAG,CAAC,UAAU,EAAE,UAAU,KAAK,IAAI,CAAC;AACzD,MAAM,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,UAAU,GAAG,UAAU,EAAE,eAAe,EAAE,oBAAoB,KAAK,IAAI,CAAC;AAE3G,MAAM,MAAM,WAAW,CAAC,QAAQ,SAAS,QAAQ,GAAG,QAAQ,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,KAAK,IAAI,CAAC;AACvH,MAAM,MAAM,gBAAgB,CAAC,QAAQ,SAAS,QAAQ,GAAG,QAAQ,IAAI,CACnE,KAAK,EAAE,UAAU,GAAG,UAAU,EAC9B,IAAI,EAAE,QAAQ,EACd,UAAU,EAAE,UAAU,KACnB,IAAI,CAAC;AACV,MAAM,MAAM,cAAc,CAAC,QAAQ,SAAS,QAAQ,GAAG,QAAQ,IAAI,CACjE,KAAK,EAAE,UAAU,GAAG,UAAU,EAC9B,IAAI,EAAE,QAAQ,EACd,UAAU,EAAE,UAAU,EACtB,eAAe,EAAE,oBAAoB,KAClC,IAAI,CAAC;AAEV,MAAM,MAAM,iBAAiB,GAAG,CAAC,IAAI,EAAE,QAAQ,GAAG,UAAU,KAAK,OAAO,CAAC;AAEzE;;GAEG;AACH,MAAM,MAAM,iBAAiB,CAAC,QAAQ,SAAS,QAAQ,IAAI;IACzD,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC/C,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,OAAO,EAAE,eAAe,CAAC;IACzB,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,EAAE,MAAM,CAAC;CACjB,CAAC;AAEF,MAAM,MAAM,WAAW,GAAG,IAAI,GAAG,GAAG,CAAC;AACrC,MAAM,MAAM,eAAe,GAAG,GAAG,MAAM,GAAG,WAAW,EAAE,GAAG,MAAM,CAAC;AAEjE,MAAM,MAAM,OAAO,GACf,eAAe,GACf;IACE,GAAG,CAAC,EAAE,eAAe,CAAC;IACtB,KAAK,CAAC,EAAE,eAAe,CAAC;IACxB,MAAM,CAAC,EAAE,eAAe,CAAC;IACzB,IAAI,CAAC,EAAE,eAAe,CAAC;IACvB,CAAC,CAAC,EAAE,eAAe,CAAC;IACpB,CAAC,CAAC,EAAE,eAAe,CAAC;CACrB,CAAC;AAEN;;GAEG;AACH,MAAM,MAAM,kBAAkB,CAAC,QAAQ,SAAS,QAAQ,GAAG,QAAQ,IAAI;IACrE,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAC7B,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;IAC7B,WAAW,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC;IAClC,KAAK,CAAC,EAAE,CAAC,QAAQ,GAAG;QAAE,EAAE,EAAE,MAAM,CAAA;KAAE,CAAC,EAAE,CAAC;CACvC,CAAC;AAEF;;;;;;;;;;GAUG;AACH,MAAM,MAAM,QAAQ,GAAG;IACrB,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,IAAI,EAAE,MAAM,CAAC;CACd,CAAC;AAEF,MAAM,MAAM,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AAE7C,MAAM,MAAM,QAAQ,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAExC;;;;;;;GAOG;AACH,oBAAY,eAAe;IACzB,IAAI,SAAS;IACb,QAAQ,aAAa;IACrB,UAAU,eAAe;CAC1B;AAED;;GAEG;AACH,MAAM,MAAM,6BAA6B,GAAG;IAC1C,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC;IAC7B,WAAW,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC;CACnC,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,gBAAgB,GAAG,6BAA6B,GAAG;IAC7D,IAAI,CAAC,EAAE,MAAM,CAAC;CACf,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,gBAAgB,GAAG,6BAA6B,GAAG;IAC7D,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG,CAAC,QAAQ,EAAE,QAAQ,KAAK,IAAI,CAAC;AAE5D,MAAM,MAAM,cAAc,GAAG,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC5D,MAAM,MAAM,mBAAmB,GAAG,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;AACjF,MAAM,MAAM,aAAa,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO,KAAK,IAAI,CAAC;AAE5E,MAAM,MAAM,mBAAmB,GAAG,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,KAAK,IAAI,CAAC;AAEtE;;;;;;;GAOG;AACH,MAAM,MAAM,aAAa,GACrB,UAAU,GACV,YAAY,GACZ,WAAW,GACX,aAAa,GACb,eAAe,GACf,cAAc,GACd,aAAa,GACb,cAAc,CAAC;AAEnB,MAAM,MAAM,UAAU,GAAG;IACvB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,eAAe,EAAE,OAAO,CAAC;CAC1B,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG,WAAW,CAAC,cAAc,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;AAE9E,oBAAY,aAAa;IACvB,OAAO,YAAY;IACnB,IAAI,SAAS;CACd;AAED,MAAM,MAAM,aAAa,GAAG,IAAI,GAAG;IACjC,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF,MAAM,MAAM,OAAO,GAAG,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;AAE5D,MAAM,MAAM,mBAAmB,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,YAAY,GAAG,gBAAgB,CAAC,EAAE,QAAQ,CAAC,EAAE,OAAO,KAAK,IAAI,CAAC;AACxH,MAAM,MAAM,KAAK,GAAG,CAAC,KAAK,EAAE,UAAU,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;AAE5D,eAAO,MAAM,iBAAiB,EAAE,YAW/B,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG;IACzB,UAAU,EAAE,KAAK,CAAC;IAClB,OAAO,EAAE,IAAI,CAAC;IACd,IAAI,EAAE,IAAI,CAAC;IACX,UAAU,EAAE,IAAI,CAAC;IACjB,YAAY,EAAE,IAAI,CAAC;IACnB,QAAQ,EAAE,IAAI,CAAC;IACf,EAAE,EAAE,IAAI,CAAC;IACT,QAAQ,EAAE,IAAI,CAAC;IACf,UAAU,EAAE,IAAI,CAAC;IACjB,MAAM,EAAE,IAAI,CAAC;CACd,CAAC;AACF,MAAM,MAAM,oBAAoB,CAAC,QAAQ,SAAS,gBAAgB,GAAG,gBAAgB,IAAI;IACvF,+DAA+D;IAC/D,UAAU,EAAE,IAAI,CAAC;IACjB;;;OAGG;IACH,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;IACxB,+EAA+E;IAC/E,IAAI,EAAE,UAAU,CAAC;IACjB,0EAA0E;IAC1E,UAAU,EAAE,MAAM,CAAC;IACnB,wGAAwG;IACxG,YAAY,EAAE,QAAQ,CAAC;IACvB,wEAAwE;IACxE,QAAQ,EAAE,QAAQ,CAAC;IACnB,6EAA6E;IAC7E,EAAE,EAAE,UAAU,CAAC;IACf,wEAAwE;IACxE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB,sGAAsG;IACtG,UAAU,EAAE,QAAQ,CAAC;IACrB,sEAAsE;IACtE,MAAM,EAAE,QAAQ,GAAG,IAAI,CAAC;CACzB,CAAC;AAEF;;;;;GAKG;AACH,MAAM,MAAM,eAAe,CAAC,QAAQ,SAAS,gBAAgB,GAAG,gBAAgB,IAC5E,oBAAoB,CAAC,QAAQ,CAAC,GAC9B,YAAY,CAAC;AAEjB,MAAM,MAAM,oBAAoB,CAAC,QAAQ,SAAS,gBAAgB,GAAG,gBAAgB,IAAI,IAAI,CAC3F,eAAe,CAAC,QAAQ,CAAC,EACzB,YAAY,CACb,CAAC;AAEF,MAAM,MAAM,gBAAgB,CAAC,QAAQ,SAAS,gBAAgB,GAAG,gBAAgB,IAAI,CACnF,MAAM,EAAE,eAAe,CAAC,QAAQ,CAAC,KAC9B,IAAI,CAAC;AAEV,MAAM,MAAM,cAAc,GAAG,OAAO,GAAG,MAAM,CAAC;AAC9C,MAAM,MAAM,SAAS,GAAG,cAAc,GAAG,QAAQ,CAAC;AAElD,MAAM,MAAM,gBAAgB,GAAG,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC,CAAC;AAE1E,MAAM,MAAM,kBAAkB,CAAC,QAAQ,SAAS,QAAQ,GAAG,QAAQ,EAAE,QAAQ,SAAS,QAAQ,GAAG,QAAQ,IAAI,CAAC,EAC5G,KAAK,EACL,KAAK,GACN,EAAE;IACD,KAAK,EAAE,QAAQ,EAAE,CAAC;IAClB,KAAK,EAAE,QAAQ,EAAE,CAAC;CACnB,KAAK,OAAO,CAAC,OAAO,GAAG;IAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;IAAC,KAAK,EAAE,QAAQ,EAAE,CAAA;CAAE,CAAC,CAAC"}