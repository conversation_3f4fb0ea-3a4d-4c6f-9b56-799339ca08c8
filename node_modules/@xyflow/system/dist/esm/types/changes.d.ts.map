{"version": 3, "file": "changes.d.ts", "sourceRoot": "", "sources": ["../../src/types/changes.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,GAAG,CAAC;AAEpE,MAAM,MAAM,mBAAmB,GAAG;IAChC,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,YAAY,CAAC;IACnB,UAAU,CAAC,EAAE,UAAU,CAAC;IAExB,QAAQ,CAAC,EAAE,OAAO,CAAC;IAEnB,aAAa,CAAC,EAAE,OAAO,GAAG,OAAO,GAAG,QAAQ,CAAC;CAC9C,CAAC;AAEF,MAAM,MAAM,kBAAkB,GAAG;IAC/B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,UAAU,CAAC;IACjB,QAAQ,CAAC,EAAE,UAAU,CAAC;IACtB,gBAAgB,CAAC,EAAE,UAAU,CAAC;IAC9B,QAAQ,CAAC,EAAE,OAAO,CAAC;CACpB,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG;IAChC,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,QAAQ,CAAC;IACf,QAAQ,EAAE,OAAO,CAAC;CACnB,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG;IAC7B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,QAAQ,CAAC;CAChB,CAAC;AAEF,MAAM,MAAM,aAAa,CAAC,QAAQ,SAAS,QAAQ,GAAG,QAAQ,IAAI;IAChE,IAAI,EAAE,QAAQ,CAAC;IACf,IAAI,EAAE,KAAK,CAAC;IACZ,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF,MAAM,MAAM,iBAAiB,CAAC,QAAQ,SAAS,QAAQ,GAAG,QAAQ,IAAI;IACpE,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,QAAQ,CAAC;IACf,IAAI,EAAE,SAAS,CAAC;CACjB,CAAC;AAEF;;;;;;GAMG;AACH,MAAM,MAAM,UAAU,CAAC,QAAQ,SAAS,QAAQ,GAAG,QAAQ,IACvD,mBAAmB,GACnB,kBAAkB,GAClB,mBAAmB,GACnB,gBAAgB,GAChB,aAAa,CAAC,QAAQ,CAAC,GACvB,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AAEhC,MAAM,MAAM,mBAAmB,GAAG,mBAAmB,CAAC;AACtD,MAAM,MAAM,gBAAgB,GAAG,gBAAgB,CAAC;AAChD,MAAM,MAAM,aAAa,CAAC,QAAQ,SAAS,QAAQ,GAAG,QAAQ,IAAI;IAChE,IAAI,EAAE,QAAQ,CAAC;IACf,IAAI,EAAE,KAAK,CAAC;IACZ,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF,MAAM,MAAM,iBAAiB,CAAC,QAAQ,SAAS,QAAQ,GAAG,QAAQ,IAAI;IACpE,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,QAAQ,CAAC;IACf,IAAI,EAAE,SAAS,CAAC;CACjB,CAAC;AAEF;;;;;;;GAOG;AACH,MAAM,MAAM,UAAU,CAAC,QAAQ,SAAS,QAAQ,GAAG,QAAQ,IACvD,mBAAmB,GACnB,gBAAgB,GAChB,aAAa,CAAC,QAAQ,CAAC,GACvB,iBAAiB,CAAC,QAAQ,CAAC,CAAC"}