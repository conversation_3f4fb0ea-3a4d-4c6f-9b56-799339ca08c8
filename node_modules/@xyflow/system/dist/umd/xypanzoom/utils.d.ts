import { type ZoomTransform } from 'd3-zoom';
import { type D3SelectionInstance, type Viewport } from '../types';
export declare const viewChanged: (prevViewport: Viewport, eventViewport: any) => boolean;
export declare const transformToViewport: (transform: ZoomTransform) => Viewport;
export declare const viewportToTransform: ({ x, y, zoom }: Viewport) => ZoomTransform;
export declare const isWrappedWithClass: (event: any, className: string | undefined) => any;
export declare const isRightClickPan: (panOnDrag: boolean | number[], usedButton: number) => boolean;
export declare const getD3Transition: (selection: D3SelectionInstance, duration?: number, ease?: (t: number) => number, onEnd?: () => void) => D3SelectionInstance | import("d3-transition").Transition<Element, unknown, null, undefined>;
export declare const wheelDelta: (event: any) => number;
//# sourceMappingURL=utils.d.ts.map