import type { StraightEdgeProps } from '../../types';
/**
 * Component that can be used inside a custom edge to render a straight line.
 *
 * @public
 * @example
 *
 * ```tsx
 * import { StraightEdge } from '@xyflow/react';
 *
 * function CustomEdge({ sourceX, sourceY, targetX, targetY }) {
 *   return (
 *     <StraightEdge
 *       sourceX={sourceX}
 *       sourceY={sourceY}
 *       targetX={targetX}
 *       targetY={targetY}
 *     />
 *   );
 * }
 * ```
 */
declare const StraightEdge: import("react").MemoExoticComponent<({ id, sourceX, sourceY, targetX, targetY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, interactionWidth, }: StraightEdgeProps) => import("react/jsx-runtime").JSX.Element>;
/**
 * @internal
 */
declare const StraightEdgeInternal: import("react").MemoExoticComponent<({ id, sourceX, sourceY, targetX, targetY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, interactionWidth, }: StraightEdgeProps) => import("react/jsx-runtime").JSX.Element>;
export { StraightEdge, StraightEdgeInternal };
//# sourceMappingURL=StraightEdge.d.ts.map