import { JSX } from 'react';
import type { Edge, EdgeWrapperProps } from '../../types';
export declare function EdgeWrapper<EdgeType extends Edge = Edge>({ id, edgesFocusable, edgesReconnectable, elementsSelectable, onClick, onDoubleClick, onContextMenu, onMouseEnter, onMouseMove, onMouseLeave, reconnectRadius, onReconnect, onReconnectStart, onReconnectEnd, rfId, edgeTypes, noPanClassName, onError, disableKeyboardA11y, }: EdgeWrapperProps<EdgeType>): JSX.Element | null;
//# sourceMappingURL=index.d.ts.map