import { CSSProperties } from 'react';
import { ConnectionLineType } from '@xyflow/system';
import type { ConnectionLineComponent, Node } from '../../types';
type ConnectionLineWrapperProps<NodeType extends Node = Node> = {
    type: ConnectionLineType;
    component?: ConnectionLineComponent<NodeType>;
    containerStyle?: CSSProperties;
    style?: CSSProperties;
};
export declare function ConnectionLineWrapper<NodeType extends Node = Node>({ containerStyle, style, type, component, }: ConnectionLineWrapperProps<NodeType>): import("react/jsx-runtime").JSX.Element | null;
export {};
//# sourceMappingURL=index.d.ts.map