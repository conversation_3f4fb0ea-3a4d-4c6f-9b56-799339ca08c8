{"version": 3, "file": "general.d.ts", "sourceRoot": "", "sources": ["../../src/types/general.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,OAAO,CAAC;AACtC,OAAO,EACL,iBAAiB,EACjB,kBAAkB,EAClB,SAAS,EACT,MAAM,EACN,WAAW,EACX,OAAO,EACP,WAAW,EACX,SAAS,EACT,SAAS,EACT,UAAU,EACV,kBAAkB,EAClB,UAAU,EACV,UAAU,EACV,UAAU,EACX,MAAM,gBAAgB,CAAC;AAExB,OAAO,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,GAAG,CAAC;AAE7E;;;;;;;;;;;;GAYG;AACH,MAAM,MAAM,aAAa,CAAC,QAAQ,SAAS,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,QAAQ,CAAC,EAAE,KAAK,IAAI,CAAC;AAEpG;;;;;;;;;;;;GAYG;AACH,MAAM,MAAM,aAAa,CAAC,QAAQ,SAAS,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,QAAQ,CAAC,EAAE,KAAK,IAAI,CAAC;AAEpG,MAAM,MAAM,aAAa,CAAC,QAAQ,SAAS,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,IAAI,CAAC;AACtF,MAAM,MAAM,aAAa,CAAC,QAAQ,SAAS,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,IAAI,CAAC;AAEtF;;;;GAIG;AACH,MAAM,MAAM,QAAQ,CAAC,QAAQ,SAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,SAAS,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE;IAC1F,KAAK,EAAE,QAAQ,EAAE,CAAC;IAClB,KAAK,EAAE,QAAQ,EAAE,CAAC;CACnB,KAAK,IAAI,CAAC;AAEX,MAAM,MAAM,SAAS,GAAG,MAAM,CAC5B,MAAM,EACN,aAAa,CACX,SAAS,GAAG;IAEV,IAAI,EAAE,GAAG,CAAC;IAEV,IAAI,EAAE,GAAG,CAAC;CACX,CACF,CACF,CAAC;AAEF,MAAM,MAAM,SAAS,GAAG,MAAM,CAC5B,MAAM,EACN,aAAa,CACX,SAAS,GAAG;IAEV,IAAI,EAAE,GAAG,CAAC;IAEV,IAAI,EAAE,GAAG,CAAC;CACX,CACF,CACF,CAAC;AAEF,MAAM,MAAM,2BAA2B,CAAC,QAAQ,SAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,SAAS,IAAI,GAAG,IAAI,IAAI;IACpG,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC;IACnB,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC;CACpB,CAAC;AAEF,MAAM,MAAM,uBAAuB,CAAC,QAAQ,SAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,SAAS,IAAI,GAAG,IAAI,IAAI;IAChG,KAAK,EAAE,QAAQ,EAAE,CAAC;IAClB,KAAK,EAAE,QAAQ,EAAE,CAAC;CACnB,CAAC;AAEF,MAAM,MAAM,qBAAqB,CAAC,QAAQ,SAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,SAAS,IAAI,GAAG,IAAI,IAAI,CAC9F,MAAM,EAAE,uBAAuB,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAChD,IAAI,CAAC;AAEV;;GAEG;AACH,MAAM,MAAM,aAAa,CAAC,QAAQ,SAAS,IAAI,GAAG,IAAI,IAAI,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AAEtF;;;;;;;GAOG;AACH,MAAM,MAAM,cAAc,CAAC,QAAQ,SAAS,IAAI,GAAG,IAAI,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAExF;;GAEG;AACH,MAAM,MAAM,OAAO,CAAC,QAAQ,SAAS,IAAI,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;AAEpH;;GAEG;AACH,MAAM,MAAM,MAAM,CAAC,QAAQ,SAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,SAAS,IAAI,GAAG,IAAI,IAAI,CAC/E,iBAAiB,EAAE,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,CAAC,KACrD,IAAI,CAAC;AAEV;;GAEG;AACH,MAAM,MAAM,uBAAuB,GAAG;IACpC;;;;OAIG;IACH,MAAM,EAAE,SAAS,CAAC;IAClB;;;;OAIG;IACH,OAAO,EAAE,SAAS,CAAC;IACnB;;;;;;OAMG;IACH,MAAM,EAAE,MAAM,CAAC;IACf;;;;OAIG;IACH,OAAO,EAAE,OAAO,CAAC;IACjB;;;;;;OAMG;IACH,WAAW,EAAE,WAAW,CAAC;IACzB;;;;OAIG;IACH,WAAW,EAAE,WAAW,CAAC;IACzB;;;;;;;;;OASG;IACH,SAAS,EAAE,SAAS,CAAC;IACrB;;;;;;;;;OASG;IACH,SAAS,EAAE,SAAS,CAAC;IACrB;;;;;;;;;;OAUG;IACH,oBAAoB,EAAE,CAAC,cAAc,EAAE,UAAU,EAAE,OAAO,CAAC,EAAE;QAAE,UAAU,EAAE,OAAO,CAAA;KAAE,KAAK,UAAU,CAAC;IACpG;;;;;;;;OAQG;IACH,oBAAoB,EAAE,CAAC,YAAY,EAAE,UAAU,KAAK,UAAU,CAAC;CAChE,CAAC;AAEF,MAAM,MAAM,cAAc,CAAC,QAAQ,SAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,SAAS,IAAI,GAAG,IAAI,IAAI,kBAAkB,CACzG,QAAQ,EACR,QAAQ,CACT,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,iBAAiB,CAAC,QAAQ,SAAS,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,QAAQ,GAAG,UAAU,KAAK,OAAO,CAAC"}