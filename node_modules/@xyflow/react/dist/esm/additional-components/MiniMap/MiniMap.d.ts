import type { Node } from '../../types';
import type { MiniMapProps } from './types';
declare function MiniMapComponent<NodeType extends Node = Node>({ style, className, nodeStrokeColor, nodeColor, nodeClassName, nodeBorderRadius, nodeStrokeWidth, nodeComponent, bgColor, maskColor, maskStrokeColor, maskStrokeWidth, position, onClick, onNodeClick, pannable, zoomable, ariaLabel, inversePan, zoomStep, offsetScale, }: MiniMapProps<NodeType>): import("react/jsx-runtime").JSX.Element;
declare namespace MiniMapComponent {
    var displayName: string;
}
/**
 * The `<MiniMap />` component can be used to render an overview of your flow. It
 * renders each node as an SVG element and visualizes where the current viewport is
 * in relation to the rest of the flow.
 *
 * @public
 * @example
 *
 * ```jsx
 *import { ReactFlow, MiniMap } from '@xyflow/react';
 *
 *export default function Flow() {
 *  return (
 *    <ReactFlow nodes={[...]]} edges={[...]]}>
 *      <MiniMap nodeStrokeWidth={3} />
 *    </ReactFlow>
 *  );
 *}
 *```
 */
export declare const MiniMap: typeof MiniMapComponent;
export {};
//# sourceMappingURL=MiniMap.d.ts.map