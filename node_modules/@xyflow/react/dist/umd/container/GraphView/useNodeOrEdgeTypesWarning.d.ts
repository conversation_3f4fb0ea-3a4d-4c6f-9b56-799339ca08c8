import type { EdgeTypes, NodeTypes } from '../../types';
/**
 * This hook warns the user if nodeTypes or edgeTypes changed.
 * It is only used in development mode.
 *
 * @internal
 */
export declare function useNodeOrEdgeTypesWarning(nodeOrEdgeTypes?: NodeTypes): void;
export declare function useNodeOrEdgeTypesWarning(nodeOrEdgeTypes?: EdgeTypes): void;
//# sourceMappingURL=useNodeOrEdgeTypesWarning.d.ts.map