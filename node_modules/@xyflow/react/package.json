{"name": "@xyflow/react", "version": "12.8.2", "description": "React Flow - A highly customizable React library for building node-based editors and interactive flow charts.", "keywords": ["react", "node-based UI", "graph", "diagram", "workflow", "react-flow", "xyflow"], "repository": {"type": "git", "url": "https://github.com/xyflow/xyflow.git", "directory": "packages/react"}, "homepage": "https://reactflow.dev", "bugs": {"url": "https://github.com/xyflow/xyflow/issues"}, "files": ["dist"], "source": "src/index.ts", "main": "dist/umd/index.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"node": {"types": "./dist/esm/index.d.ts", "module": "./dist/esm/index.js", "require": "./dist/umd/index.js", "import": "./dist/esm/index.mjs"}, "browser": {"import": "./dist/esm/index.js", "require": "./dist/umd/index.js"}, "default": "./dist/esm/index.js"}, "./dist/base.css": "./dist/base.css", "./dist/style.css": "./dist/style.css"}, "sideEffects": ["*.css"], "license": "MIT", "publishConfig": {"access": "public"}, "dependencies": {"classcat": "^5.0.3", "zustand": "^4.4.0", "@xyflow/system": "0.0.66"}, "peerDependencies": {"react": ">=17", "react-dom": ">=17"}, "devDependencies": {"@types/node": "^18.7.16", "@types/react": ">=17", "@types/react-dom": ">=17", "autoprefixer": "^10.4.15", "cssnano": "^6.0.1", "postcss": "^8.4.21", "postcss-cli": "^11.0.0", "postcss-combine-duplicated-selectors": "^10.0.3", "postcss-import": "^15.1.0", "postcss-nested": "^6.0.0", "postcss-rename": "^0.6.1", "react": "^18.2.0", "typescript": "5.4.5", "@xyflow/rollup-config": "0.0.0", "@xyflow/eslint-config": "0.0.1", "@xyflow/tsconfig": "0.0.0"}, "rollup": {"globals": {"classcat": "cc", "zustand": "zustand", "zustand/shallow": "zustandShallow"}, "name": "ReactFlow"}, "scripts": {"dev": "concurrently \"rollup --config node:@xyflow/rollup-config --watch\" pnpm:css-watch", "build": "rollup --config node:@xyflow/rollup-config --environment NODE_ENV:production && npm run css", "css": "postcss src/styles/{base,style}.css --config ./../../tooling/postcss-config/ --dir dist ", "css-watch": "pnpm css --watch", "lint": "eslint --ext .js,.jsx,.ts,.tsx src", "typecheck": "tsc --noEmit"}}