# Chatan AI Flow

一个基于 @xyflow/react 构建的智能工作流编辑器，采用 Simple-AI 设计风格。

## 🎨 设计特色

- 🎯 **Simple-AI 风格** - 借鉴 simple-ai.dev 的现代化设计语言
- 🌓 **深色/浅色主题** - 支持主题切换，适应不同使用场景
- 🤖 **AI 专用组件** - 专门为 AI 工作流设计的节点类型
- ✨ **现代化 UI** - 使用 shadcn/ui 设计系统和配色方案

## 🚀 功能特性

- ✅ 可拖动的节点
- ✅ 可缩放的画布
- ✅ 节点之间的连接
- ✅ AI 风格的自定义节点组件
- ✅ 小地图导航
- ✅ 控制面板（缩放、适应视图等）
- ✅ 响应式设计
- ✅ TypeScript 支持
- ✅ 性能优化（使用 React.memo）
- ✅ 主题切换功能
- ✅ 平滑动画和过渡效果

## 🛠️ 技术栈

- **React 18** - 前端框架
- **TypeScript** - 类型安全
- **@xyflow/react** - 流程图库
- **Vite** - 构建工具
- **CSS3** - 样式设计
- **Simple-AI 设计系统** - 现代化 UI 组件库风格

## 📁 项目结构

```
src/
├── components/
│   ├── FlowChart.tsx      # 主要的流程图组件
│   ├── CustomNode.tsx     # AI 风格的自定义节点组件
│   ├── CustomNode.css     # 节点样式（Simple-AI 风格）
│   ├── ThemeToggle.tsx    # 主题切换组件
│   └── ThemeToggle.css    # 主题切换样式
├── App.tsx                # 主应用组件
├── App.css                # 应用样式
├── main.tsx               # 应用入口
└── index.css              # 全局样式（设计系统）
```

## 安装和运行

### 前提条件

确保你的系统已安装：
- Node.js (版本 16 或更高)
- npm 或 yarn

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 启动开发服务器

```bash
npm run dev
# 或
yarn dev
```

### 构建生产版本

```bash
npm run build
# 或
yarn build
```

## 使用说明

### 基本操作

1. **拖动节点**: 点击并拖动任意节点来移动它
2. **缩放画布**: 使用鼠标滚轮或控制面板的缩放按钮
3. **连接节点**: 从一个节点的输出点拖动到另一个节点的输入点
4. **选择节点**: 点击节点来选择它
5. **平移画布**: 在空白区域拖动来平移整个画布

### 控制面板功能

- **+/-**: 放大/缩小画布
- **⌂**: 适应视图（显示所有节点）
- **🔒**: 锁定/解锁交互
- **📷**: 截图功能

### 小地图

右下角的小地图可以：
- 显示整个流程图的概览
- 快速导航到不同区域
- 显示当前视图位置

## 自定义开发

### 创建新的节点类型

1. 在 `src/components/` 目录下创建新的节点组件
2. 使用 `React.memo` 包装组件以优化性能
3. 在 `FlowChart.tsx` 中注册新的节点类型

```typescript
// 示例：创建新节点类型
const nodeTypes = {
  customNode: CustomNode,
  newNodeType: NewNodeComponent, // 添加新类型
};
```

### 修改节点样式

编辑 `src/components/CustomNode.css` 文件来自定义节点外观。

### 添加新功能

- 修改 `FlowChart.tsx` 来添加新的交互功能
- 使用 `useCallback` 和 `useMemo` 来优化性能
- 遵循 React Flow 的最佳实践

## 性能优化

项目已实现以下性能优化：

1. **React.memo**: 自定义节点使用 memo 包装
2. **useCallback**: 事件处理函数使用 useCallback
3. **useMemo**: 节点类型定义使用 useMemo 缓存
4. **CSS优化**: 使用 CSS transitions 而非 JavaScript 动画

## 浏览器支持

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 许可证

MIT License
