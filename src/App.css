.App {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 2rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.app-header h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.app-header p {
  margin: 0.5rem 0 0 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.app-main {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* React Flow 容器样式调整 */
.react-flow {
  background: #f8fafc;
}

/* 控制面板样式 */
.react-flow__controls {
  bottom: 20px;
  left: 20px;
}

.react-flow__controls button {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
  transition: all 0.2s ease;
}

.react-flow__controls button:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

/* 小地图样式 */
.react-flow__minimap {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
}

/* 边的样式 */
.react-flow__edge-path {
  stroke: #6b7280;
  stroke-width: 2;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #ef4444;
  stroke-width: 3;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .react-flow {
    background: #1f2937;
  }

  .react-flow__controls button {
    background: #374151;
    border-color: #4b5563;
    color: #e5e7eb;
  }

  .react-flow__controls button:hover {
    background: #4b5563;
    border-color: #6b7280;
  }

  .react-flow__minimap {
    background: #374151;
    border-color: #4b5563;
  }

  .react-flow__edge-path {
    stroke: #9ca3af;
  }
}
