/* 自定义节点样式 */
.custom-node {
  background: #ffffff;
  border: 2px solid #1a192b;
  border-radius: 8px;
  padding: 12px;
  min-width: 150px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.custom-node:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.custom-node.selected {
  border-color: #ff6b6b;
  box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.2);
}

.node-content {
  text-align: center;
}

.node-header {
  font-size: 14px;
  font-weight: 600;
  color: #1a192b;
  margin-bottom: 4px;
}

.node-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 自定义连接点样式 */
.custom-handle {
  width: 10px;
  height: 10px;
  background: #555;
  border: 2px solid #fff;
  border-radius: 50%;
}

.custom-handle.react-flow__handle-top {
  top: -6px;
}

.custom-handle.react-flow__handle-bottom {
  bottom: -6px;
}

.custom-handle:hover {
  background: #ff6b6b;
}

.custom-handle.connecting {
  background: #ff6b6b;
}

.custom-handle.valid {
  background: #55dd99;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .custom-node {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }

  .node-header {
    color: #e2e8f0;
  }

  .node-description {
    color: #a0aec0;
  }

  .custom-handle {
    background: #718096;
    border-color: #2d3748;
  }
}
