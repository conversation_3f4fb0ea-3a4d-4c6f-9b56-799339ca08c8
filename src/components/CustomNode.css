/* AI Node Styles - Simple AI Inspired */
.ai-node {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: var(--radius);
  min-width: 200px;
  max-width: 280px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.ai-node:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
  border-color: hsl(var(--ai-primary) / 0.5);
}

.ai-node.selected {
  border-color: hsl(var(--ai-primary));
  box-shadow: 0 0 0 2px hsl(var(--ai-primary) / 0.2);
}

/* Node type specific styling */
.ai-node.input-node {
  border-left: 4px solid hsl(var(--ai-primary));
}

.ai-node.process-node {
  border-left: 4px solid hsl(var(--ai-secondary));
}

.ai-node.output-node {
  border-left: 4px solid hsl(var(--ai-accent));
}

.ai-node.ai-node {
  border-left: 4px solid hsl(var(--ai-warning));
  background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--ai-warning) / 0.05) 100%);
}

/* Node Header */
.ai-node-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border-bottom: 1px solid hsl(var(--border));
  background: hsl(var(--muted) / 0.3);
}

.ai-node-icon {
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: hsl(var(--primary) / 0.1);
  border-radius: calc(var(--radius) - 2px);
}

.ai-node-title {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 600;
  color: hsl(var(--foreground));
  line-height: 1.2;
}

.ai-node-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: all 0.2s ease-in-out;
}

.ai-node-status.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

/* Node Content */
.ai-node-content {
  padding: 1rem;
}

.ai-node-description {
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
  line-height: 1.4;
  margin: 0;
}

/* AI Handle Styles */
.ai-handle {
  width: 12px;
  height: 12px;
  background: hsl(var(--muted-foreground));
  border: 2px solid hsl(var(--card));
  border-radius: 50%;
  transition: all 0.2s ease-in-out;
}

.ai-handle:hover {
  background: hsl(var(--ai-primary));
  transform: scale(1.2);
  box-shadow: 0 0 0 4px hsl(var(--ai-primary) / 0.2);
}

.ai-handle-input {
  top: -6px;
}

.ai-handle-output {
  bottom: -6px;
}

.ai-handle.connectinghandle {
  background: hsl(var(--ai-warning));
  box-shadow: 0 0 0 4px hsl(var(--ai-warning) / 0.3);
  animation: connecting-pulse 1s infinite;
}

.ai-handle.valid {
  background: hsl(var(--ai-success));
  box-shadow: 0 0 0 4px hsl(var(--ai-success) / 0.3);
}

@keyframes connecting-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-node {
    min-width: 160px;
    max-width: 200px;
  }

  .ai-node-header {
    padding: 0.75rem;
    gap: 0.5rem;
  }

  .ai-node-content {
    padding: 0.75rem;
  }

  .ai-node-icon {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 1rem;
  }
}
