import React, { memo } from 'react';
import { Handle, Position, NodeProps } from '@xyflow/react';
import './CustomNode.css';

// 定义节点数据类型
interface CustomNodeData {
  label: string;
  description?: string;
}

// 自定义节点组件
const CustomNode: React.FC<NodeProps<CustomNodeData>> = ({ data, isConnectable }) => {
  return (
    <div className="custom-node">
      {/* 输入连接点 */}
      <Handle
        type="target"
        position={Position.Top}
        id="input"
        isConnectable={isConnectable}
        className="custom-handle"
      />
      
      <div className="node-content">
        <div className="node-header">
          <strong>{data.label}</strong>
        </div>
        {data.description && (
          <div className="node-description">
            {data.description}
          </div>
        )}
      </div>

      {/* 输出连接点 */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="output"
        isConnectable={isConnectable}
        className="custom-handle"
      />
    </div>
  );
};

// 使用memo优化性能，避免不必要的重新渲染
export default memo(CustomNode);
