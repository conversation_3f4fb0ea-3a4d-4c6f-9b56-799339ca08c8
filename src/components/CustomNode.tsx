import React, { memo } from 'react';
import { Handle, Position, NodeProps } from '@xyflow/react';
import './CustomNode.css';

// 定义节点数据类型
interface CustomNodeData {
  label: string;
  description?: string;
  type?: 'input' | 'process' | 'output' | 'ai';
  status?: 'idle' | 'processing' | 'success' | 'error';
  icon?: string;
}

// 获取节点图标
const getNodeIcon = (type: string = 'process') => {
  const icons = {
    input: '📝',
    process: '⚙️',
    output: '📤',
    ai: '🤖'
  };
  return icons[type as keyof typeof icons] || '⚙️';
};

// 获取状态指示器
const getStatusIndicator = (status: string = 'idle') => {
  const statusConfig = {
    idle: { color: 'var(--muted-foreground)', pulse: false },
    processing: { color: 'var(--ai-warning)', pulse: true },
    success: { color: 'var(--ai-success)', pulse: false },
    error: { color: 'var(--ai-error)', pulse: false }
  };
  return statusConfig[status as keyof typeof statusConfig] || statusConfig.idle;
};

// 自定义节点组件
const CustomNode: React.FC<NodeProps<CustomNodeData>> = ({
  data,
  isConnectable,
  selected
}) => {
  const nodeType = data.type || 'process';
  const status = data.status || 'idle';
  const statusConfig = getStatusIndicator(status);

  return (
    <div className={`ai-node ${selected ? 'selected' : ''} ${nodeType}-node`}>
      {/* 输入连接点 */}
      <Handle
        type="target"
        position={Position.Top}
        id="input"
        isConnectable={isConnectable}
        className="ai-handle ai-handle-input"
      />

      {/* 节点头部 */}
      <div className="ai-node-header">
        <div className="ai-node-icon">
          {data.icon || getNodeIcon(nodeType)}
        </div>
        <div className="ai-node-title">
          {data.label}
        </div>
        <div
          className={`ai-node-status ${statusConfig.pulse ? 'pulse' : ''}`}
          style={{ backgroundColor: `hsl(${statusConfig.color})` }}
        />
      </div>

      {/* 节点内容 */}
      {data.description && (
        <div className="ai-node-content">
          <div className="ai-node-description">
            {data.description}
          </div>
        </div>
      )}

      {/* 输出连接点 */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="output"
        isConnectable={isConnectable}
        className="ai-handle ai-handle-output"
      />
    </div>
  );
};

// 使用memo优化性能，避免不必要的重新渲染
export default memo(CustomNode);
