import React, { use<PERSON><PERSON>back, useMemo } from 'react';
import {
  ReactFlow,
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  BackgroundVariant,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import CustomNode from './CustomNode';

// 定义节点类型
const nodeTypes = {
  customNode: CustomNode,
};

// 初始节点数据 - AI工作流风格
const initialNodes: Node[] = [
  {
    id: '1',
    type: 'customNode',
    position: { x: 250, y: 50 },
    data: {
      label: 'Text Input',
      description: '用户输入文本内容',
      type: 'input',
      status: 'idle',
      icon: '📝'
    },
  },
  {
    id: '2',
    type: 'customNode',
    position: { x: 100, y: 200 },
    data: {
      label: 'AI Processor',
      description: '使用AI模型处理文本',
      type: 'ai',
      status: 'processing',
      icon: '🤖'
    },
  },
  {
    id: '3',
    type: 'customNode',
    position: { x: 400, y: 200 },
    data: {
      label: 'Text Analyzer',
      description: '分析文本内容和情感',
      type: 'process',
      status: 'success',
      icon: '🔍'
    },
  },
  {
    id: '4',
    type: 'customNode',
    position: { x: 250, y: 350 },
    data: {
      label: 'Output Display',
      description: '显示处理结果',
      type: 'output',
      status: 'idle',
      icon: '📊'
    },
  },
];

// 初始边数据
const initialEdges: Edge[] = [
  { id: 'e1-2', source: '1', target: '2' },
  { id: 'e1-3', source: '1', target: '3' },
  { id: 'e2-4', source: '2', target: '4' },
  { id: 'e3-4', source: '3', target: '4' },
];

const FlowChart: React.FC = () => {
  const [nodes, , onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  // 连接节点的回调函数
  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges],
  );

  // 使用useMemo缓存nodeTypes以避免重新渲染
  const memoizedNodeTypes = useMemo(() => nodeTypes as any, []);

  return (
    <div style={{ width: '100vw', height: '100vh' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={memoizedNodeTypes}
        fitView
        attributionPosition="top-right"
      >
        <Controls />
        <MiniMap />
        <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
      </ReactFlow>
    </div>
  );
};

export default FlowChart;
