import React, { useCallback, useMemo } from 'react';
import {
  ReactFlow,
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  BackgroundVariant,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import CustomNode from './CustomNode';

// 定义节点类型
const nodeTypes = {
  customNode: CustomNode,
};

// 初始节点数据
const initialNodes: Node[] = [
  {
    id: '1',
    type: 'customNode',
    position: { x: 250, y: 25 },
    data: { label: '节点 1', description: '这是第一个自定义节点' },
  },
  {
    id: '2',
    type: 'customNode',
    position: { x: 100, y: 125 },
    data: { label: '节点 2', description: '这是第二个自定义节点' },
  },
  {
    id: '3',
    type: 'customNode',
    position: { x: 400, y: 125 },
    data: { label: '节点 3', description: '这是第三个自定义节点' },
  },
];

// 初始边数据
const initialEdges: Edge[] = [
  { id: 'e1-2', source: '1', target: '2' },
  { id: 'e1-3', source: '1', target: '3' },
];

const FlowChart: React.FC = () => {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  // 连接节点的回调函数
  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges],
  );

  // 使用useMemo缓存nodeTypes以避免重新渲染
  const memoizedNodeTypes = useMemo(() => nodeTypes, []);

  return (
    <div style={{ width: '100vw', height: '100vh' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={memoizedNodeTypes}
        fitView
        attributionPosition="top-right"
      >
        <Controls />
        <MiniMap />
        <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
      </ReactFlow>
    </div>
  );
};

export default FlowChart;
