.theme-toggle {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 50;
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 2rem;
  padding: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.theme-toggle:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.theme-toggle:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

.theme-toggle-track {
  width: 3rem;
  height: 1.5rem;
  background: hsl(var(--muted));
  border-radius: 1rem;
  position: relative;
  transition: background-color 0.2s ease-in-out;
}

.theme-toggle-thumb {
  position: absolute;
  top: 0.125rem;
  width: 1.25rem;
  height: 1.25rem;
  background: hsl(var(--background));
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.theme-toggle-thumb.light {
  left: 0.125rem;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

.theme-toggle-thumb.dark {
  left: 1.625rem;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
}

.theme-icon {
  font-size: 0.75rem;
  line-height: 1;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* 动画效果 */
@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

.theme-toggle:active .theme-toggle-thumb {
  animation: bounce 0.3s ease-in-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .theme-toggle {
    top: 0.75rem;
    right: 0.75rem;
  }
  
  .theme-toggle-track {
    width: 2.5rem;
    height: 1.25rem;
  }
  
  .theme-toggle-thumb {
    width: 1rem;
    height: 1rem;
  }
  
  .theme-toggle-thumb.dark {
    left: 1.375rem;
  }
  
  .theme-icon {
    font-size: 0.625rem;
  }
}
