@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Design System Colors - Light Theme */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.75rem;

  /* AI-specific colors */
  --ai-primary: 221.2 83.2% 53.3%;
  --ai-secondary: 142.1 76.2% 36.3%;
  --ai-accent: 262.1 83.3% 57.8%;
  --ai-warning: 32.5 94.6% 43.7%;
  --ai-success: 142.1 76.2% 36.3%;
  --ai-error: 0 84.2% 60.2%;

  /* Typography */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

/* Dark theme */
.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
}

* {
  border-color: hsl(var(--border));
}

body {
  margin: 0;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  min-height: 100vh;
  font-feature-settings: "rlig" 1, "calt" 1;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
}

h1 {
  font-size: 2.25rem;
  font-weight: 700;
}

h2 {
  font-size: 1.875rem;
}

h3 {
  font-size: 1.5rem;
}

button {
  border-radius: var(--radius);
  border: 1px solid hsl(var(--border));
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  font-family: inherit;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

button:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

button:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* React Flow specific styles - Simple AI inspired */
.react-flow {
  background: hsl(var(--background));
  font-family: inherit;
}

.react-flow__node {
  font-size: 0.875rem;
  font-weight: 500;
}

.react-flow__handle {
  width: 12px;
  height: 12px;
  background: hsl(var(--muted-foreground));
  border: 2px solid hsl(var(--background));
  border-radius: 50%;
  transition: all 0.2s ease-in-out;
}

.react-flow__handle:hover {
  background: hsl(var(--ai-primary));
  transform: scale(1.1);
}

.react-flow__handle.connectinghandle {
  background: hsl(var(--ai-warning));
  box-shadow: 0 0 0 4px hsla(var(--ai-warning), 0.2);
}

.react-flow__handle.valid {
  background: hsl(var(--ai-success));
  box-shadow: 0 0 0 4px hsla(var(--ai-success), 0.2);
}

/* Edge styles */
.react-flow__edge-path {
  stroke: hsl(var(--muted-foreground));
  stroke-width: 2;
  transition: all 0.2s ease-in-out;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: hsl(var(--ai-primary));
  stroke-width: 3;
}

.react-flow__edge:hover .react-flow__edge-path {
  stroke: hsl(var(--ai-primary));
  stroke-width: 2.5;
}

/* Controls styling */
.react-flow__controls {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: var(--radius);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.react-flow__controls button {
  background: transparent;
  border: none;
  color: hsl(var(--muted-foreground));
  padding: 0.5rem;
  border-radius: calc(var(--radius) - 2px);
}

.react-flow__controls button:hover {
  background: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

/* MiniMap styling */
.react-flow__minimap {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: var(--radius);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.react-flow__minimap-mask {
  fill: hsl(var(--primary) / 0.1);
  stroke: hsl(var(--primary));
  stroke-width: 2;
}

/* Background patterns */
.react-flow__background {
  background: hsl(var(--background));
}

.react-flow__background .react-flow__background-pattern {
  fill: hsl(var(--muted-foreground) / 0.1);
}
